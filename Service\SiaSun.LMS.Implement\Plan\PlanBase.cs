﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    public class PlanBase : S_BaseService
    {
        public PlanBase()
        {
        }

        /// <summary>
        /// 创建入出库计划
        /// </summary>
        /// <param name="mPLAN_MAIN"></param>
        /// <param name="dtPLAN_LIST"></param>
        /// <param name="PLAN_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, DataTable dtPLAN_LIST, out int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            PLAN_ID = 0;

            try
            {
                this._P_Base_House.BeginTransaction();

                if (null != this._P_PLAN_MAIN.GetModel(mPLAN_MAIN.PLAN_ID))
                {
                    if (mPLAN_MAIN.PLAN_STATUS.Equals(SiaSun.LMS.Enum.PLAN_STATUS.Complete.ToString()))
                    {
                        bResult = false;

                        sResult = string.Format("{0}单号已经完成 不能编辑!", mPLAN_MAIN.PLAN_CODE);

                        this._P_Base_House.RollBackTransaction();

                        return bResult;
                    }

                    this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                else
                {
                    //PLAN_CODE可重复时 以下判断是不是应该注释掉
                    if (!string.IsNullOrEmpty(mPLAN_MAIN.PLAN_CODE)
                        && null != this._P_PLAN_MAIN.GetModelPlanCode(mPLAN_MAIN.PLAN_CODE, mPLAN_MAIN.PLAN_GROUP, mPLAN_MAIN.PLAN_RELATIVE_CODE))
                    {
                        bResult = false;

                        sResult = string.Format("{0}单号已经存在!", mPLAN_MAIN.PLAN_CODE);

                        this._P_Base_House.RollBackTransaction();

                        return bResult;
                    }

                    this._P_PLAN_MAIN.Add(mPLAN_MAIN);
                }


                foreach (DataRow drPLAN_LIST in dtPLAN_LIST.Rows)
                {

                    if (drPLAN_LIST.RowState != DataRowState.Deleted)
                    {
                        drPLAN_LIST["PLAN_ID"] = mPLAN_MAIN.PLAN_ID;
                        drPLAN_LIST["PLAN_LIST_ID"] = this._P_Base_House.GetPrimaryID("PLAN_LIST");
                    }
                }

                this.Save(dtPLAN_LIST, "PLAN_LIST");

                //mPLAN_MAIN.PLAN_CODE = "LK" + mPLAN_MAIN.PLAN_ID.ToString();

                //this._P_PLAN_MAIN.Update(mPLAN_MAIN);

                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }

            PLAN_ID = mPLAN_MAIN.PLAN_ID;

            return bResult;
        }

        /// <summary>创建
        /// 创建
        /// </summary>
        /// <param name="mPLAN_MAIN">计划模型</param>
        /// <param name="lsPLAN_LIST">计划列表</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, IList<Model.PLAN_LIST> lsPLAN_LIST, bool isTrans, out int PLAN_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            PLAN_ID = 0;

            try
            {
                this._P_Base_House.BeginTransaction(isTrans);

                if (null != this._P_PLAN_MAIN.GetModel(mPLAN_MAIN.PLAN_ID))
                {
                    if (mPLAN_MAIN.PLAN_STATUS.Equals(SiaSun.LMS.Enum.PLAN_STATUS.Complete.ToString()))
                    {
                        bResult = false;
                        sResult = string.Format("{0}单号已经完成 不能编辑!", mPLAN_MAIN.PLAN_CODE);
                        return bResult;
                    }
                    this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                else
                {
                    if (!string.IsNullOrEmpty(mPLAN_MAIN.PLAN_CODE) && null != this._P_PLAN_MAIN.GetModelPlanCode(mPLAN_MAIN.PLAN_CODE, mPLAN_MAIN.PLAN_GROUP, mPLAN_MAIN.PLAN_RELATIVE_CODE))
                    {
                        bResult = false;
                        sResult = string.Format("{0}单号已经存在!", mPLAN_MAIN.PLAN_CODE);
                        return bResult;
                    }
                    this._P_PLAN_MAIN.Add(mPLAN_MAIN);
                }

                foreach (SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    bResult = this._S_GoodsService.GoodsPropertyCheck(mPLAN_LIST.GOODS_ID, mPLAN_LIST, out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("属性校验错误: {0}", sResult);
                        return bResult;
                    }

                    if (mPLAN_LIST.PLAN_ID.Equals(0))
                    {
                        mPLAN_LIST.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                        this._P_PLAN_LIST.Add(mPLAN_LIST);
                    }
                    else
                    {
                        this._P_PLAN_LIST.Update(mPLAN_LIST);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(isTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(isTrans);
                }
            }

            PLAN_ID = mPLAN_MAIN.PLAN_ID;
            return bResult;
        }


        /// <summary>
        /// 创建 wdz add 2017-11-27
        /// </summary>
        public bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, IList<Model.PLAN_LIST> lsPLAN_LIST, out int PLAN_ID, out string sResult)
        {
            return this.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out PLAN_ID, out sResult);
        }

        /// <summary>
        /// 创建
        /// </summary>
        public bool PlanImport(Model.PLAN_MAIN mPLAN_MAIN,
                              IList<Model.PLAN_LIST> lsPLAN_LIST,
                              out int PLAN_ID,
                              out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            PLAN_ID = 0;

            //try
            //{
            //    this._P_Base_House.BeginTransaction();

            //    if (null != this._P_PLAN_MAIN.GetModel(mPLAN_MAIN.PLAN_ID))
            //    {
            //        if (mPLAN_MAIN.PLAN_STATUS.Equals(SiaSun.LMS.Enum.PLAN_STATUS.Complete.ToString()))
            //        {
            //            bResult = false;

            //            sResult = string.Format("{0}单号已经完成 不能编辑!", mPLAN_MAIN.PLAN_CODE);

            //            return bResult;
            //        }

            //        this._P_PLAN_MAIN.Update(mPLAN_MAIN);
            //    }
            //    else
            //    {
            //        if (null != this._P_PLAN_MAIN.GetModel(mPLAN_MAIN.PLAN_CODE))
            //        {
            //            bResult = false;

            //            sResult = string.Format("{0}单号已经存在!", mPLAN_MAIN.PLAN_CODE);

            //            return bResult;
            //        }

            //        this._P_PLAN_MAIN.Add(mPLAN_MAIN);

            //        mPLAN_RELATE.PLAN_ID = mPLAN_MAIN.PLAN_ID;

            //        this._P_PLAN_RELATE.Add(mPLAN_RELATE);
            //    }

            //    foreach (SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
            //    {
            //        bResult = this._S_Goods.GoodsPropertyCheck(mPLAN_LIST.GOODS_ID, mPLAN_LIST, out sResult);

            //        if (!bResult)
            //        {
            //            sResult = string.Format("属性校验错误: {0}", sResult);

            //            return bResult;
            //        }

            //        if (mPLAN_LIST.PLAN_ID.Equals(0))
            //        {
            //            mPLAN_LIST.PLAN_ID = mPLAN_MAIN.PLAN_ID;
            //            this._P_PLAN_LIST.Add(mPLAN_LIST);
            //        }
            //        else
            //        {
            //            this._P_PLAN_LIST.Update(mPLAN_LIST);
            //        }
            //    }

            //}
            //catch (Exception ex)
            //{
            //    bResult = false;

            //    sResult = ex.Message;
            //}
            //finally
            //{
            //    if (bResult)
            //    {
            //        this._P_Base_House.CommitTransaction();
            //    }
            //    else
            //    {
            //        this._P_Base_House.RollBackTransaction();
            //    }
            //}

            //PLAN_ID = mPLAN_MAIN.PLAN_ID;

            return bResult;
        }

        /// <summary>取消
        /// 取消
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanCancel(int PLAN_ID, out string sResult)
        {
            return this.PlanCancel(PLAN_ID, "Yes", true, true, out sResult);
        }

        /// <summary>
        /// wdz add 2018-10-25
        /// 取消 调整是否忽略计划状态
        /// </summary>
        public bool PlanCancel(int planId,string noticeWms, bool notIgnorePlanStatus, bool isTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(isTrans);

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(planId);
                if (null == mPLAN_MAIN)
                {
                    bResult = false;
                    sResult = string.Format("未找到计划{0}", planId.ToString());
                    return bResult;
                }

                //if (mPLAN_MAIN.PLAN_FLAG == Enum.TASK_SOURCE.WMS.ToString())
                //{
                //    bResult = false;
                //    sResult = string.Format("WMS导入计划{0}不能删除", PLAN_ID.ToString());
                //    return bResult;
                //}

                IList<Model.MANAGE_MAIN> lsMANAGE_MAIN = this._P_MANAGE_MAIN.GetListPlanID(planId);
                if (lsMANAGE_MAIN.Count > 0)
                {
                    bResult = false;
                    sResult = string.Format("此计划存在任务。不能删除");
                    return bResult;
                }

                IList<Model.RECORD_MAIN> lsRECORD_MAIN = this._P_RECORD_MAIN.GetListPlanId(mPLAN_MAIN.PLAN_ID);
                if (lsRECORD_MAIN.Count > 0)
                {
                    bResult = false;
                    sResult = string.Format("此计划已经存在出入库记录。不能删除");
                    return bResult;
                }

                if (notIgnorePlanStatus && mPLAN_MAIN.PLAN_STATUS != Enum.PLAN_STATUS.Waiting.ToString())
                {
                    bResult = false;
                    sResult = string.Format("此计划状态不是等待执行，不能删除");
                    return bResult;
                }
                IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID);
                foreach (Model.PLAN_LIST itemPLAN_LIST in lsPLAN_LIST)
                {
                    if (itemPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY + itemPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY > 0)
                    {
                        bResult = false;
                        sResult = string.Format("此计划单分配数量或者完成数量不为0，不能删除");
                        return bResult;
                    }
                }

                //wdz add 2018-01-27
                //盘点取消后更新工作站状态
                if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                {
                    Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel_BY_CELL_ID(this._P_WH_CELL.GetModel(mPLAN_MAIN.PLAN_INOUT_STATION).CELL_ID);
                    if (mT_PICK_STATION != null && mT_PICK_STATION.PROPERTY3 != Enum.FLAG.Enable.ToString("d"))
                    {
                        mT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");
                        this._P_T_PICK_STATION.Update(mT_PICK_STATION);
                    }
                }

                this._P_PLAN_LIST.DeletePlanID(planId);
                this._P_PLAN_MAIN.Delete(planId);

                //wdz add 2018-01-13 计划删除时通知WMS
                string isNoticeWms = string.Empty;
                if (mPLAN_MAIN.PLAN_FLAG == Enum.TASK_SOURCE.WMS.ToString() && noticeWms=="Yes" &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanPick.ToString() ? mPLAN_MAIN.PLAN_GROUP : mPLAN_MAIN.PLAN_CODE, false, " 计划已删除", out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(isTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(isTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// wdz add 2018-05-02
        /// 取消计划 默认带事务 接口失败取消时调用
        /// </summary>
        public bool PlanCancel(int planId, bool notIgnorePlanStatus, out string sResult)
        {
            return this.PlanCancel(planId, "No", notIgnorePlanStatus, true, out sResult);
        }

        /// <summary>检验计划是否可以完成
        /// 检验计划是否可以完成
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanCheckComplete(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID.ToString());

                return bResult;
            }

            IList<SiaSun.LMS.Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListNoComplete(PLAN_ID);


            if (lsPLAN_LIST.Count <= 0)
            {
                bResult = false;

                return bResult;
            }


            return bResult;
        }

        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanComplete(int PLAN_ID, out string sResult)
        {
            return this.PlanComplete(PLAN_ID, true, out sResult);
        }

        /// <summary>完成-支持事务
        /// 完成-支持事务
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanComplete(int PLAN_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID.ToString());

                return bResult;
            }

            //wdz add 2017-12-06
            if (mPLAN_MAIN.PLAN_STATUS == Enum.PLAN_STATUS.Complete.ToString() || mPLAN_MAIN.PLAN_STATUS == Enum.PLAN_STATUS.Finish.ToString())
            {
                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                mPLAN_MAIN.PLAN_END_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                //XCJT ALTER 2016-12-8
                //mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Complete.ToString();
                mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Finish.ToString();
                this._P_PLAN_MAIN.Update(mPLAN_MAIN);

                //wdz add 2018-01-27
                //盘点完成后更新工作站状态
                if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString()|| 
                    mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheckManual.ToString())
                {
                    Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel_BY_CELL_ID(this._P_WH_CELL.GetModel(mPLAN_MAIN.PLAN_INOUT_STATION).CELL_ID);
                    if (mT_PICK_STATION != null && mT_PICK_STATION.PROPERTY3 != Enum.FLAG.Enable.ToString("d"))
                    {
                        mT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");
                        this._P_T_PICK_STATION.Update(mT_PICK_STATION);
                    }
                }

                //接口调用
                string isNoticeWms = string.Empty;
                if (mPLAN_MAIN.PLAN_FLAG==Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanOutEmptyBox.ToString() ||
                        mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanOutEmerg.ToString() ||
                        mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString())
                    {
                        //空箱出库、紧急配料出库、整理不满箱出库
                        bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mPLAN_MAIN.PLAN_CODE, out sResult);
                    }
                    else if(mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                    {
                        //盘点
                        bResult = new Interface.countResultReturnFromWCS().NotifyMethod(mPLAN_MAIN.PLAN_ID, out sResult);
                    }
                    else if(mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanPick.ToString())
                    {
                        //拣选  查找与本PLAN ID不同但GROUP相同 并且没有完成的，查出的结果为0说明整个WMS的拣选单
                        DataTable dtPickPlanNotComplete = this.GetList(string.Format("select 1 from PLAN_MAIN where PLAN_STATUS!='{0}' and PLAN_GROUP='{1}' and PLAN_ID != {2}",Enum.PLAN_STATUS.Finish.ToString(),mPLAN_MAIN.PLAN_GROUP,mPLAN_MAIN.PLAN_ID));
                        if (dtPickPlanNotComplete != null && dtPickPlanNotComplete.Rows.Count == 0)
                        {
                            bResult = new Interface.miniloadPickConfirmFromWCS().NotifyMethod(mPLAN_MAIN.PLAN_ID, mPLAN_MAIN.PLAN_GROUP, out sResult);
                        }
                    }
                }
                if (!bResult)
                {
                    return bResult;
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// 计划执行
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanExecute(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID);

                return bResult;
            }
            try
            {
                this._P_Base_House.BeginTransaction();

                if (string.IsNullOrEmpty(mPLAN_MAIN.PLAN_BEGIN_TIME))
                    mPLAN_MAIN.PLAN_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Executing.ToString();

                this._P_PLAN_MAIN.Update(mPLAN_MAIN);


                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>计划执行默认动作
        /// 计划执行默认动作
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanExcuteDefault(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            string ACTION_EVENT = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            DataTable dtPlan = this.GetList(string.Format("SELECT * FROM V_PLAN WHERE PLAN_ID = {0}", PLAN_ID));

            DataTable dtACTION = this._S_FlowService.PlanGetAction(PLAN_ID.ToString(), "1");

            if (dtACTION.Rows.Count.Equals(0))
            {
                return bResult;
            }

            foreach (DataRow drPlan in dtPlan.Rows)
            {
                foreach (DataRow drACTION in dtACTION.Rows)
                {
                    IList<SiaSun.LMS.Model.FLOW_PARA> lsFLOW_PARA = this._S_FlowService.FlowGetParameters("FLOW_PLAN");

                    string[] aFLOW_PARA = new string[lsFLOW_PARA.Count];

                    int i = 0;

                    foreach (SiaSun.LMS.Model.FLOW_PARA mFLOW_PARA in lsFLOW_PARA)
                    {
                        aFLOW_PARA[i] = drPlan[mFLOW_PARA.FLOW_PARA_CODE].ToString();

                        i++;
                    }

                    SiaSun.LMS.Model.PLAN_ACTION_EXCUTE mPLAN_ACTION_EXCUTE = new SiaSun.LMS.Model.PLAN_ACTION_EXCUTE();

                    mPLAN_ACTION_EXCUTE.PLAN_ID = Convert.ToInt32(drPlan["PLAN_ID"]);

                    mPLAN_ACTION_EXCUTE.ACTION_EVENT = string.Format(drACTION["FLOW_ACTION_EVENT"].ToString(), aFLOW_PARA);

                    bResult = this._S_FlowService.EventExecute(mPLAN_ACTION_EXCUTE.ACTION_EVENT, out sResult);
                }
            }

            if (bResult)
            {
                mPLAN_MAIN.PLAN_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                bResult = this.PlanExcuteDefault(PLAN_ID, out sResult);
            }

            return bResult;
        }

        /// <summary>
        /// 计划暂停
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanPause(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID);

                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction();

                mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Waiting.ToString();

                this._P_PLAN_MAIN.Update(mPLAN_MAIN);

                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>
        /// 计划完成时，更新ERP中间表中 xcjt
        /// </summary>
        private bool PlanCompleteUpdateErpFlag(int PLAN_ID)
        {
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);
                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanCompleteUpdateErpFlag()未能获取计划 计划ID{0}", PLAN_ID);
                    return bResult;
                }
                if (mPLAN_MAIN.PLAN_FLAG == "0")
                {
                    bResult = false;
                    sResult = string.Format("PlanCompleteUpdateErpFlag()未处理非导入计划 计划ID{0}", PLAN_ID);
                    return bResult;
                }

                IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID);

                foreach (var mPLAN_LIST in lsPLAN_LIST)
                {
                    Model.MID_TASK mMID_TASK = this._P_MID_TASK.GetModel(int.Parse(mPLAN_LIST.PLAN_LIST_CODE));
                    mMID_TASK.FINISH_FLAG = Enum.ERP_TASK_FLAG.Finish.ToString("d");
                    this._P_MID_TASK.Update(mMID_TASK);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("计划完成后更新ERP中间表中FINISH_FLAG时发生异常 {0}", ex.Message);
            }
            finally
            {
                if (!bResult)
                {
                    //如果更新失败则记录日志
                    this._S_SystemService.CreateSysLog(Enum.LogThread.Interface, "System", Enum.LOG_LEVEL.Error, string.Format("PlanBase.PlanCompleteUpdateErpFlag():ERP导入的任务完成后更新中间表中的FINISH_QTY失败 {0}", sResult));
                }
            }

            return bResult;
        }


        /// <summary>
        /// 出库计划自动选定货位出库
        /// </summary>
        public bool PlanOutDownLoad(int planId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(planId);
                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanOutDownLoad:未找到计划信息 计划ID_{0}", planId);
                    return bResult;
                }

                IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(planId);
                if (lsPLAN_LIST == null || lsPLAN_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanOutDownLoad:未找到计划列表 计划ID_{0}", planId);
                    return bResult;
                }

                if (string.IsNullOrEmpty(mPLAN_MAIN.PLAN_INOUT_STATION))
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanOutDownLoad:计划站台信息不能为空 计划单号_{0}", mPLAN_MAIN.PLAN_CODE);
                    return bResult;
                }
                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mPLAN_MAIN.PLAN_INOUT_STATION);
                if (mWH_CELL_END == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanOutDownLoad:计划站台信息有误 值_{0}", mPLAN_MAIN.PLAN_INOUT_STATION);
                    return bResult;
                }

                //齐套箱出库计划在执行前，先调整远近叉顺序 mxh add 2023-01-13 16:58:25.690 
                if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                {
                    List<Model.PLAN_LIST> nearPLAN_LIST = new List<Model.PLAN_LIST>();
                    List<Model.PLAN_LIST> farPLAN_LIST = new List<Model.PLAN_LIST>();

                    foreach (Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                    {
                        var querySql = string.Format(@"select * from STORAGE_MAIN 
                                               left join WH_CELL on STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID
                                               where STORAGE_ID in (select STORAGE_ID from STORAGE_LIST 
                                                                    where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}') 
                                               order by SHELF_TYPE desc",
                                               mPLAN_LIST.GOODS_PROPERTY6,
                                               mPLAN_LIST.GOODS_PROPERTY7,
                                               mPLAN_LIST.GOODS_PROPERTY8);

                        DataTable dtStorageEnable = this.GetList(querySql);
                        if (dtStorageEnable != null && dtStorageEnable.Rows.Count > 0 && dtStorageEnable.Rows[0]["CELL_FORK_TYPE"].ToString().Equals("Far"))
                        {
                            farPLAN_LIST.Add(mPLAN_LIST);
                        }
                        else
                        {
                            nearPLAN_LIST.Add(mPLAN_LIST);
                        }
                    }

                    nearPLAN_LIST.AddRange(farPLAN_LIST);
                    lsPLAN_LIST = nearPLAN_LIST;
                }


                foreach (Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanOutEmptyBox.ToString()
                        || mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanOutEmerg.ToString())
                    {
                        //空箱出库计划、紧急出库计划
                        bResult = this.PlanOutListDownLoad(mPLAN_LIST, mPLAN_MAIN, mWH_CELL_END.CELL_ID, false, out sResult);
                    }
                    else if(mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString()|| 
                        mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheckManual.ToString())
                    {
                        //wdz add 2018-09-29 暂停后再盘点下架时先清空qty
                        mPLAN_LIST.PLAN_LIST_QUANTITY = 0;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        //wdz alter 2018-09-27 修正本计划单内物料在同一个计划的其他计划单下达任务时包含了的bug
                        //DataTable dtGoodsInManage = this.GetList(string.Format("select 0 from MANAGE_LIST where GOODS_ID={0}",mPLAN_LIST.GOODS_ID));
                        //if (dtGoodsInManage != null && dtGoodsInManage.Rows.Count != 0)
                        //{
                        //    bResult = false;
                        //    sResult = string.Format("PlanBase.PlanOutDownLoad:当前任务中存在待盘点物料，请等待任务执行完成后再盘点 物料ID_{0}", mPLAN_LIST.GOODS_ID);
                        //    return bResult;
                        //}
                        DataTable dtGoodsInManage = this.GetList(string.Format("select MANAGE_LIST_ID from MANAGE_LIST where GOODS_ID={0}", mPLAN_LIST.GOODS_ID));
                        if (dtGoodsInManage != null && dtGoodsInManage.Rows.Count > 0)
                        {
                            foreach (DataRow item in dtGoodsInManage.Rows)
                            {
                                Model.MANAGE_LIST mMANAGE_LIST = this._P_MANAGE_LIST.GetModel(Convert.ToInt32(item["MANAGE_LIST_ID"]));
                                if (mMANAGE_LIST == null)
                                {
                                    bResult = false;
                                    sResult = string.Format("PlanBase.PlanOutDownLoad:根据MANAGE_LIST_ID为找到任务列表_MANAGE_LIST_ID[{0}]", item["MANAGE_LIST_ID"]);
                                    return bResult;
                                }
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);
                                if (mMANAGE_MAIN == null)
                                {
                                    bResult = false;
                                    sResult = string.Format("PlanBase.PlanOutDownLoad:根据MANAGE_LIST_ID为找到任务信息_MANAGE_LIST_ID[{0}]_MANAGE_ID[{1}]", item["MANAGE_LIST_ID"], mMANAGE_LIST.MANAGE_ID);
                                    return bResult;
                                }

                                //如果当前物料存在的任务是这个盘点计划其他计划单生成的下架任务，则视为任务生成成功
                                if (mMANAGE_MAIN.PLAN_ID == mPLAN_MAIN.PLAN_ID && mMANAGE_LIST.PLAN_LIST_ID == 0
                                    && mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageCheckDown.ToString())
                                {
                                    mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;

                                    this._P_MANAGE_LIST.Update(mMANAGE_LIST);

                                    mPLAN_LIST.PLAN_LIST_QUANTITY++;
                                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY++;

                                    this._P_PLAN_LIST.Update(mPLAN_LIST);
                                }
                                else
                                {
                                    bResult = false;
                                    sResult = string.Format("PlanBase.PlanOutDownLoad:当前任务中存在待盘点物料_请等待任务执行完成后再盘点_物料ID[{0}]", mPLAN_LIST.GOODS_ID);
                                    return bResult;
                                }
                            }
                        }

                        DataTable dtAllStorage = this.GetList(string.Format("select * from V_STORAGE_LIST where GOODS_ID= {0} and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null", mPLAN_LIST.GOODS_ID));
                        if (dtAllStorage == null || dtAllStorage.Rows.Count == 0)
                        {
                            //if (lsPLAN_LIST.Count < 2)
                            //{
                            //    bResult = false;
                            //    sResult = string.Format("PlanBase.PlanOutDownLoad:立库区没有可用于盘点的非齐套物料库存_物料ID_{0}", mPLAN_LIST.GOODS_ID);
                            //    return bResult;
                            //}
                            //else
                            {
                                //如果遇到盘点的库存为0 则增加一条qty为0的plan_list
                                mPLAN_LIST.PLAN_LIST_QUANTITY = 0;
                                mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = 0;
                                mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY = 0;
                                mPLAN_LIST.PLAN_LIST_QUANTITY_APPEND = 0;
                                this._P_PLAN_LIST.Update(mPLAN_LIST);
                                continue;
                            }
                        }

                        DataTable dtGoodsLockStatus = this.GetList(string.Format("select 0 from V_STORAGE_LIST where GOODS_ID ={0} and STORAGE_LOCK_QUANTITY != 0 ", mPLAN_LIST.GOODS_ID));
                        if (dtGoodsLockStatus != null && dtGoodsLockStatus.Rows.Count != 0)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanOutDownLoad:当前物料存在锁定库存，请解锁后盘点 物料ID_{0}", mPLAN_LIST.GOODS_ID);
                            return bResult;
                        }

                        DataTable dtStorageRouteDisable = this.GetList(string.Format("select * from V_STORAGE_LIST t where GOODS_ID= {0} and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null  and DEVICE_CODE not in (select START_DEVICE from IO_CONTROL_ROUTE t where END_DEVICE='{1}' and CONTROL_ROUTE_STATUS=1 and CONTROL_ROUTE_MANAGE=1 )", mPLAN_LIST.GOODS_ID, mWH_CELL_END.CELL_CODE));
                        if (dtStorageRouteDisable != null && dtStorageRouteDisable.Rows.Count != 0)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanOutDownLoad:不是所有的盘点物料都可以下架，请查看设备状态是否全部可用 物料ID_{0}", mPLAN_LIST.GOODS_ID);
                            return bResult;
                        }

                        //只验证第一颗盘点物料下达时站台的可用性
                        if (lsPLAN_LIST.IndexOf(mPLAN_LIST) == 0)
                        {
                            Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel_BY_CELL_ID(mWH_CELL_END.CELL_ID);
                            if (mT_PICK_STATION == null || mT_PICK_STATION.PROPERTY3 != Enum.FLAG.Enable.ToString("d"))
                            {
                                bResult = false;
                                sResult = string.Format("PlanBase.PlanOutDownLoad:未找到盘点计划指定的工作站或工作站当前不可用_工作站-{0}", mT_PICK_STATION == null ? "" : mT_PICK_STATION.STATION_NAME);
                                return bResult;
                            }
                            mT_PICK_STATION.PROPERTY3 = Enum.FLAG.Disable.ToString("d");
                            this._P_T_PICK_STATION.Update(mT_PICK_STATION);
                        }

                        //盘点计划
                        bResult = this.PlanCheckListExecute(mPLAN_LIST, mPLAN_MAIN, mWH_CELL_END.CELL_ID, false, out sResult);
                    }
                    else if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                    {
                        //齐套箱出库计划
                        bResult = this.PlanKitOutListExecute(mPLAN_LIST, mPLAN_MAIN, mWH_CELL_END.CELL_ID, false, out sResult);
                    }
                    else if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString())
                    {
                        //wdz add 2019-07-27
                        //重新定义一下整理计划，包含整理未满箱和合箱两种流程
                        //整理未满箱的意思是将某一物料A的所有在库n个箱子下架，将所有A整理到少于n个箱子中，从而减小A占用的格子数
                        //合箱的意思是将多个箱中非空的格子里的物料合并到一个箱子的多格中
                        //生成计划时整理未满箱要指定具体的物料，合箱要选择待整理箱物料

                        //wdz add 2019-07-07
                        DataTable dtWorkMode = this.GetList(string.Format("select * from SYS_PARAMETER where PARAMETER_KEY='WorkingMode-{0}' and PARAMETER_VALUE ='Arrange'", mPLAN_MAIN.PLAN_INOUT_STATION));
                        if (dtWorkMode == null || dtWorkMode.Rows.Count < 1)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanOutDownLoad:指定的整理工位[{0}]的工作模式不是[合箱模式]", mPLAN_MAIN.PLAN_INOUT_STATION);
                            return bResult;
                        }

                        //wdz add 2019-03-05 如果整理虚拟箱有货则提示失败
                        Model.STORAGE_MAIN mSTORAGE_MAIN_CHECK = this._P_STORAGE_MAIN.GetModelStockBarcode("V" + mPLAN_MAIN.PLAN_INOUT_STATION);
                        if (mSTORAGE_MAIN_CHECK == null)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanOutDownLoad:未找到虚拟箱库存信息_虚拟箱[{0}]", "V" + mPLAN_MAIN.PLAN_INOUT_STATION);
                            return bResult;
                        }
                        IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_CHECK.STORAGE_ID);
                        if (lsSTORAGE_LIST != null && lsSTORAGE_LIST.Count > 0)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanOutDownLoad:合箱计划执行时发现虚拟箱存在库存_虚拟箱[{0}]_库存条数[{1}]", "V" + mPLAN_MAIN.PLAN_INOUT_STATION, lsSTORAGE_LIST.Count);
                            return bResult;
                        }

                        //整理未满箱出库计划
                        bResult = this.PlanArrangeOutListExecute(mPLAN_LIST, mPLAN_MAIN, mWH_CELL_END.CELL_ID, false, out sResult);
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanOutDownLoad:计划类型有误或未配置动作_计划类型值[{0}]", mPLAN_MAIN.PLAN_TYPE_CODE);
                        return bResult;
                    }

                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                ////如果齐套箱出库计划单都已删除则通知唯智计划失败
                //if(mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                //{
                //    lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID);
                //    if(lsPLAN_LIST.Count<1)
                //    {
                //        bResult = false;
                //        sResult = string.Format("PlanBase.PlanOutDownLoad:齐套箱出库计划执行失败_所有需求物料均以包含在其他相同出库位置的出库任务中_无需再次指定出库");
                //        return bResult;
                //    }
                //}

                //spring 22/09/01 齐套出库计算总箱数并给任务编号
                if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                {
                    string strSql = string.Format("select MANAGE_ID from MANAGE_MAIN where PLAN_ID = {0} group by MANAGE_ID", planId);

                    DataTable dtManage = this.GetList(strSql);

                    if (dtManage != null && dtManage.Rows.Count > 0)
                    {
                        int all_count = dtManage.Rows.Count;//该齐套出库计划下的全部任务数
                        int iNum = 1;//当前箱数
                        foreach (DataRow dr in dtManage.Rows)
                        {

                            Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(Convert.ToInt32(dr["MANAGE_ID"]));
                            if (mMANAGE_MAIN != null)
                            {
                                //更新io_control中集货单总任务数字段
                                Model.IO_CONTROL iO_CONTROL = this._P_IO_CONTROL.GetModelManageID(mMANAGE_MAIN.MANAGE_ID);
                                if (iO_CONTROL != null)
                                {
                                    iO_CONTROL.TASK_COUNT = all_count.ToString();
                                    this._P_IO_CONTROL.Update(iO_CONTROL);
                                }

                                mMANAGE_MAIN.BACKUP_FIELD1 = all_count.ToString();
                                if (string.IsNullOrEmpty(mMANAGE_MAIN.BACKUP_FIELD2))
                                {
                                    mMANAGE_MAIN.BACKUP_FIELD2 = iNum.ToString();
                                }
                                else
                                {
                                    this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("PlanBase.PlanOutDownLoad:计划id[{0}]齐套出库,MANAGE_ID[{1}]任务中已存在编号[{2}],跳过编号[{3}]"
                                        , planId, mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.BACKUP_FIELD2, iNum));
                                }
                            }
                            this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                            iNum++;
                        }
                    }

                    
                }

                //更新计划状态
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Executing.ToString();
                this._P_PLAN_MAIN.Update(mPLAN_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.PlanDownLoad:异常 {0}", ex.ToString());
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        /// <summary>
        /// 出库计划单执行
        /// </summary>
        public bool PlanOutListDownLoad(Model.PLAN_LIST mPLAN_LIST, Model.PLAN_MAIN mPLAN_MAIN, int endCellId, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            int sumQuantity = 0;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID);
                if (mGOODS_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanOutListDownLoad:未找到计划物料 物料ID_{0} 计划列表ID_{1}", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }

                //判断当前计划单是否以包含在任务中(前提是不同PLAN_LIST不能包含同种GOODS_ID)
                DataTable dtManageEnable = this.GetList(string.Format("select MANAGE_LIST.MANAGE_LIST_ID,MANAGE_LIST.MANAGE_LIST_QUANTITY from MANAGE_LIST inner join MANAGE_MAIN on MANAGE_LIST.MANAGE_ID=MANAGE_MAIN.MANAGE_ID where MANAGE_MAIN.PLAN_ID ={0} and MANAGE_LIST.GOODS_ID={1} and MANAGE_LIST.PLAN_LIST_ID=0", mPLAN_MAIN.PLAN_ID, mPLAN_LIST.GOODS_ID));
                int goodsInManageQty = 0;
                string sGoodsInManageQty = dtManageEnable.Compute("Sum(MANAGE_LIST_QUANTITY)", "true").ToString();

                if (dtManageEnable != null && dtManageEnable.Rows.Count > 0 &&
                    int.TryParse(sGoodsInManageQty, out goodsInManageQty) && goodsInManageQty >0)
                {
                    foreach(DataRow dr in dtManageEnable.Rows)
                    {
                        if(mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY < mPLAN_LIST.PLAN_LIST_QUANTITY)
                        {
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += int.Parse(dr["MANAGE_LIST_QUANTITY"].ToString());
                            this._P_PLAN_LIST.Update(mPLAN_LIST);

                            Model.MANAGE_LIST mMANAGE_LIST = this._P_MANAGE_LIST.GetModel(int.Parse(dr["MANAGE_LIST_ID"].ToString()));
                            mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                            this._P_MANAGE_LIST.Update(mMANAGE_LIST);
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                if(mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY >= mPLAN_LIST.PLAN_LIST_QUANTITY)
                {
                    return bResult;
                }

                DataTable dtLANEWAY = null;
                //过滤掉包含任务单号、项目号和WBS号的物料 以及锁定的物料  以及被拣选工作站表记为异常的库存
                //string strWhere = string.Format("and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null and (STORAGE_LOCK_QUANTITY = 0 or STORAGE_LOCK_QUANTITY is null)");
                string strWhere = string.Format("and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null and (STORAGE_LOCK_QUANTITY = 0 or STORAGE_LOCK_QUANTITY is null)   and (GOODS_PROPERTY2 is null or GOODS_PROPERTY2 !='1') ");
                bResult = this._S_CellService.OutLaneWayGetList(endCellId, mPLAN_LIST.GOODS_ID, strWhere, out dtLANEWAY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                foreach (DataRow drLANEWAY in dtLANEWAY.Rows)
                {
                    sumQuantity += Convert.ToInt32(drLANEWAY["STORAGE_BALANCE"]);
                }
                bResult = sumQuantity >= Convert.ToInt32(mPLAN_LIST.PLAN_LIST_QUANTITY) - Convert.ToInt32(mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY);
                if (!bResult)
                {
                    sResult = string.Format("PlanBase.PlanOutListDownLoad:计划执行失败 计划出库数量为[{1}]_当前可用库存为[{0}]_物料编码[{2}],", sumQuantity, mPLAN_LIST.PLAN_LIST_QUANTITY.ToString(), mGOODS_MAIN.GOODS_CODE);
                    return bResult;
                }

                DataView dvLANEWAY = dtLANEWAY.DefaultView;
                dvLANEWAY.RowFilter = "STORAGE_BALANCE > 0";
                dvLANEWAY.Sort = "PRIOR_WEIGHT desc ,OUT_TASK_BALANCE asc,DATEWEIGHT asc ,STORAGE_BALANCE desc";

                while (mPLAN_LIST.PLAN_LIST_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                {
                    DataRowView drv0 = dvLANEWAY[0];

                    if (dvLANEWAY.Count <= 0)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanOutListDownLoad:满足条件的库存数量不足");
                        return bResult;
                    }

                    int laneWay = Convert.ToInt32(drv0["LANE_WAY"]);
                    int startCellId = 0;
                    decimal orderedQuantity = 0;
                    string stockBarcode = string.Empty;

                    bResult = this._S_CellService.CellOutAllocate(laneWay, endCellId, mPLAN_LIST.GOODS_ID, strWhere, 0, out startCellId, out stockBarcode, out orderedQuantity, out sResult);

                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanOutListDownLoad:未找到立库库存 箱条码_{0}", stockBarcode);
                        return bResult;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanOutListDownLoad:未找到库存列表 箱条码_{0}", stockBarcode);
                        return bResult;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode(mSTORAGE_MAIN.STOCK_BARCODE);
                    if (mMANAGE_MAIN != null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanOutListDownLoad:料箱在任务中无法出库 箱条码_{0}", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = endCellId;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = mPLAN_MAIN.PLAN_LEVEL;
                    mMANAGE_MAIN.MANAGE_OPERATOR = mPLAN_MAIN.PLAN_CREATER;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = mPLAN_MAIN.PLAN_CODE;
                    mMANAGE_MAIN.MANAGE_SOURCE = mPLAN_MAIN.PLAN_FLAG;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE == 
                        Enum.PLAN_TYPE_CODE.PlanOutEmptyBox.ToString() ? Enum.MANAGE_TYPE.ManageStockOut.ToString() : Enum.MANAGE_TYPE.ManageOut.ToString();
                    mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                    mMANAGE_MAIN.START_CELL_ID = startCellId;
                    mMANAGE_MAIN.STOCK_BARCODE = stockBarcode;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        if (itemSTORAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                        {
                            mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                            mMANAGE_LIST.STORAGE_LIST_ID = itemSTORAGE_LIST.STORAGE_LIST_ID;
                        }
                        //2020-10-15 20:01:53
                        mMANAGE_LIST.ORIGIN_ENTRY_TIME = itemSTORAGE_LIST.ENTRY_TIME;
                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, true, false }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    decimal dLANEWAY_QUANTITY_SUB = Convert.ToDecimal(drv0.Row["STORAGE_BALANCE"]) - orderedQuantity;

                    drv0.Row["STORAGE_BALANCE"] = dLANEWAY_QUANTITY_SUB;

                    Decimal priorWeight = Convert.ToDecimal(drv0.Row["PRIOR_WEIGHT"]);
                    if (priorWeight > 0)
                    {
                        drv0.Row["PRIOR_WEIGHT"] = priorWeight - 1;
                    }

                    if (dLANEWAY_QUANTITY_SUB > 0)
                    {
                        drv0.Row["OUT_TASK_BALANCE"] = Convert.ToDecimal(drv0.Row["OUT_TASK_BALANCE"]) + 1;
                    }

                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY + orderedQuantity;
                    this._P_PLAN_LIST.Update(mPLAN_LIST);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.PlanOutListDownLoad:程序执行中发生异常 信息_{0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// 盘点计划单执行
        /// </summary>
        public bool PlanCheckListExecute(Model.PLAN_LIST mPLAN_LIST, Model.PLAN_MAIN mPLAN_MAIN, int endCellId, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID);
                if (mGOODS_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanCheckListExecute:未找到计划物料_物料ID[{0}]_计划列表ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }

                DataTable dtLANEWAY = null;
                //过滤掉包含任务单号、项目号和WBS号的物料
                string strWhere = string.Format("and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null ");
                bResult = this._S_CellService.OutLaneWayGetList(endCellId, mPLAN_LIST.GOODS_ID, strWhere, out dtLANEWAY, out sResult);
                if (!bResult)
                {
                    //wdz alter 2018-09-27 为找到任何库存是由于前序的PLAN_LIST生成任务时把这颗料全都带出来了
                    //return bResult;
                    this.CreateSysLog(Enum.LogThread.Plan, "System", Enum.LOG_LEVEL.Critical, string.Format("PlanBase.PlanCheckListExecute:当前计划单对应的物料已全部随其他计划单盘点下架_计划CODE[{0}]_计划单ID[{1}]", mPLAN_MAIN.PLAN_CODE, mPLAN_LIST.PLAN_LIST_ID));
                    return true;
                }

                int totalBoxQuantity = 0;
                if (!int.TryParse(dtLANEWAY.Compute("sum(boxCount)", "true").ToString(), out totalBoxQuantity))
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanCheckListExecute:未能获取库存非齐套物料总箱数_物料ID[{0}]_计划列表ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }

                double totalGoodsQuantity1 = 0;
                DataTable dtStorageAll = this.GetList(string.Format("select sum(STORAGE_LIST_QUANTITY) as TOTALQTY from STORAGE_LIST t where GOODS_ID={0} and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null ", mPLAN_LIST.GOODS_ID));
                if (dtStorageAll == null || dtStorageAll.Rows.Count != 1 || !double.TryParse(dtStorageAll.Rows[0]["TOTALQTY"].ToString(), out totalGoodsQuantity1) || totalGoodsQuantity1 < 1)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanCheckListExecute:计算非齐套物料总数量失败_物料ID[{0}]_计划列表ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }

                //wdz alter 2018-09-17
                //mPLAN_LIST.PLAN_LIST_QUANTITY = totalBoxQuantity;
                mPLAN_LIST.PLAN_LIST_QUANTITY += totalBoxQuantity;
                this._P_PLAN_LIST.Update(mPLAN_LIST);

                DataView dvLANEWAY = dtLANEWAY.DefaultView;
                dvLANEWAY.RowFilter = "STORAGE_BALANCE > 0";
                dvLANEWAY.Sort = "OUT_TASK_BALANCE asc ,boxCount desc";
                
                while (mPLAN_LIST.PLAN_LIST_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                {
                    DataRowView drv0 = dvLANEWAY[0];

                    int startCellId = 0;
                    decimal orderedQuantity = 0;
                    string stockBarcode = string.Empty;
                    int laneWay = Convert.ToInt32(drv0["LANE_WAY"]);

                    bResult = this._S_CellService.CellOutAllocate(laneWay, endCellId, mPLAN_LIST.GOODS_ID, strWhere, 0, out startCellId, out stockBarcode, out orderedQuantity, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanCheckListExecute:未找到立库库存_箱条码[{0}]", stockBarcode);
                        return bResult;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanCheckListExecute:未找到库存列表_箱条码[{0}]", stockBarcode);
                        return bResult;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = endCellId;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = mPLAN_MAIN.PLAN_LEVEL;
                    mMANAGE_MAIN.MANAGE_OPERATOR = mPLAN_MAIN.PLAN_CREATER;
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageCheckDown.ToString();
                    mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                    mMANAGE_MAIN.START_CELL_ID = startCellId;
                    mMANAGE_MAIN.STOCK_BARCODE = stockBarcode;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = mPLAN_MAIN.PLAN_CODE;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = 0;
                        if (itemSTORAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                        {
                            mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                        }
                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, false, true }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    decimal dLANEWAY_QUANTITY_SUB = Convert.ToDecimal(drv0["STORAGE_BALANCE"]) - orderedQuantity;

                    drv0["STORAGE_BALANCE"] = dLANEWAY_QUANTITY_SUB;
                    drv0["OUT_TASK_BALANCE"] = Convert.ToDecimal(drv0["OUT_TASK_BALANCE"]) + 1;
                    drv0["boxCount"] = Convert.ToDecimal(drv0["boxCount"]) - 1;

                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY++;
                    this._P_PLAN_LIST.Update(mPLAN_LIST);
                }

                double totalGoodsQuantity2 = 0;
                DataTable dtStorageAllInManage = this.GetList(string.Format("select sum(MANAGE_LIST_QUANTITY) as TOTALQTY  from MANAGE_MAIN left join MANAGE_LIST on MANAGE_MAIN.MANAGE_ID = MANAGE_LIST.MANAGE_ID where PLAN_TYPE_CODE = 'PlanCheck' and MANAGE_TYPE_CODE = 'ManageCheckDown' and MANAGE_RELATE_CODE = '{0}' and GOODS_ID = {1} ", mPLAN_MAIN.PLAN_CODE, mPLAN_LIST.GOODS_ID));
                if (dtStorageAllInManage == null || dtStorageAllInManage.Rows.Count != 1 || !double.TryParse(dtStorageAllInManage.Rows[0]["TOTALQTY"].ToString(), out totalGoodsQuantity2) || totalGoodsQuantity2 < 1)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanCheckListExecute:计算物料生成的盘点下架总数量失败_物料ID[{0}]_计划列表ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }
                if (totalGoodsQuantity1 != totalGoodsQuantity2)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanCheckListExecute:物料立库总数量与生成的盘点下架任务数量不等_无法准确盘点_物料ID[{0}]_计划列表ID[{1}]_立库总数[{2}]_盘点下架任务总数[{3}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID, totalGoodsQuantity1, totalGoodsQuantity2);
                    return bResult;
                }

                #region //////////
                //foreach (DataRow dr in dtLANEWAY.Rows)
                //{
                //    decimal manageQuantity = 0;
                //    decimal storageQuantity = Convert.ToInt32(dr["STORAGE_BALANCE"]);

                //    while (manageQuantity < storageQuantity)
                //    {
                //        int laneWay = Convert.ToInt32(dr["LANE_WAY"]);
                //        int startCellId = 0;
                //        decimal orderedQuantity = 0;
                //        string stockBarcode = string.Empty;

                //        bResult = this._S_CellService.CellOutAllocate(laneWay, endCellId, mPLAN_LIST.GOODS_ID, string.Empty, 0, out startCellId, out stockBarcode, out orderedQuantity, out sResult);

                //        manageQuantity += orderedQuantity;

                //        Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                //        if (mSTORAGE_MAIN == null)
                //        {
                //            bResult = false;
                //            sResult = string.Format("PlanBase.PlanCheckListExecute:未找到立库库存 箱条码_{0}", stockBarcode);
                //            return bResult;
                //        }
                //        IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                //        if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                //        {
                //            bResult = false;
                //            sResult = string.Format("PlanBase.PlanCheckListExecute:未找到库存列表 箱条码_{0}", stockBarcode);
                //            return bResult;
                //        }

                //        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
                //        mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                //        mMANAGE_MAIN.END_CELL_ID = endCellId;
                //        mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                //        mMANAGE_MAIN.MANAGE_LEVEL = "5";
                //        mMANAGE_MAIN.MANAGE_OPERATOR = mPLAN_MAIN.PLAN_CREATER;
                //        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                //        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                //        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageDown.ToString();
                //        mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                //        mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                //        mMANAGE_MAIN.START_CELL_ID = startCellId;
                //        mMANAGE_MAIN.STOCK_BARCODE = stockBarcode;

                //        List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                //        foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                //        {
                //            if (itemSTORAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                //            {
                //                Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                //                mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                //                mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                //                mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                //                lsMANAGE_LIST.Add(mMANAGE_LIST);
                //            }
                //        }

                //        Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                //        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, false, true }, out sResult);

                //    }
                //}

                //DataTable dtTotalQuantity = this.GetList(string.Format("select sum(MANAGE_LIST.MANAGE_LIST_QUANTITY)  from MANAGE_LIST left join MANAGE_MAIN on MANAGE_LIST.MANAGE_ID=MANAGE_MAIN.MANAGE_ID where MANAGE_LIST.GOODS_ID = {0} and MANAGE_MAIN.PLAN_TYPE_CODE = '{1}'  and MANAGE_MAIN.PLAN_ID = {2}",mPLAN_LIST.GOODS_ID,mPLAN_MAIN.PLAN_TYPE_CODE,mPLAN_MAIN.PLAN_ID));
                //int planQuantity = 0;
                //if(!int.TryParse(dtTotalQuantity.Rows[0][0].ToString(),out planQuantity))
                //{
                //    bResult = false;
                //    sResult = string.Format("PlanCheck.PlanCheckListExecute:未能转换计划总数量 值_{0}", dtLANEWAY.Rows[0][0].ToString());
                //    return bResult;
                //}
                //mPLAN_LIST.PLAN_LIST_QUANTITY = planQuantity;
                //mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = planQuantity;
                //this._P_PLAN_LIST.Update(mPLAN_LIST);
                #endregion
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.PlanCheckListExecute:程序执行中发生异常_信息[{0}]", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// 齐套箱出库计划单执行
        /// </summary>
        public bool PlanKitOutListExecute(Model.PLAN_LIST mPLAN_LIST, Model.PLAN_MAIN mPLAN_MAIN, int endCellId, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            //对于当前齐套箱计划单，如果包含齐套物料已经在其他的相同出库位置的齐套箱中，则将PLAN_LIST_QUANTITY -1
            string kitBoxCut = string.Empty;
            int kitBoxCutCount = 0;

            try 
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID);
                if (mGOODS_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到计划物料 物料ID_{0} 计划列表ID_{1}", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }
                if (mGOODS_MAIN.GOODS_CODE != "kitBox")
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanKitOutListExecute:齐套箱出库计划单中的虚拟物料有误 计划单物料编码_{0}", mGOODS_MAIN.GOODS_CODE);
                    return bResult;
                }

                //start 2020-09-28 20:15:49 齐套出库考虑双伸货位 
                //var querySql = string.Format(@"select STORAGE_ID from STORAGE_LIST where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}' group by STORAGE_ID", mPLAN_LIST.GOODS_PROPERTY6, mPLAN_LIST.GOODS_PROPERTY7, mPLAN_LIST.GOODS_PROPERTY8)
                var querySql = string.Format(@"select * from STORAGE_MAIN 
                                               left join WH_CELL on STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID
                                               where STORAGE_ID in (select STORAGE_ID from STORAGE_LIST 
                                                                    where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}') 
                                               order by SHELF_TYPE desc",                                                                 
                                               mPLAN_LIST.GOODS_PROPERTY6,
                                               mPLAN_LIST.GOODS_PROPERTY7,
                                               mPLAN_LIST.GOODS_PROPERTY8);
                //end 2020-09-28 20:15:49 齐套出库考虑双伸货位 
                DataTable dtStorageEnable = this.GetList(querySql);

                if (dtStorageEnable == null || dtStorageEnable.Rows.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到齐套箱库存 计划单号_{0} WBS号_{1}", mPLAN_MAIN.PLAN_CODE, mPLAN_LIST.GOODS_PROPERTY8);
                    return bResult;
                }

                foreach (DataRow dr in dtStorageEnable.Rows)
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(int.Parse(dr["STORAGE_ID"].ToString()));
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到立库库存 库存ID_{0}", dr["STORAGE_ID"]);
                        return bResult;
                    }
                    DataTable dtStorageArea = this.GetList(string.Format("select AREA_TYPE from WH_AREA inner join WH_CELL on WH_AREA.AREA_ID=WH_CELL.AREA_ID where CELL_ID = {0}", mSTORAGE_MAIN.CELL_ID));
                    if (dtStorageArea == null || dtStorageArea.Rows.Count == 0 || dtStorageArea.Rows[0]["AREA_TYPE"].ToString() != Enum.AREA_TYPE.LiKu.ToString())
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanKitOutListExecute:齐套箱库存区域有误或者不在立库区 库存ID_{0}", dr["STORAGE_ID"]);
                        return bResult;
                    }

                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到库存列表 库存ID_{0}", dr["STORAGE_ID"]);
                        return bResult;
                    }
                    Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode(mSTORAGE_MAIN.STOCK_BARCODE);
                    if (mMANAGE_MAIN != null)
                    {
                        //wdz add 2018-02-06 如果所要齐套箱在其他齐套箱出库任务中且终点位置相同，则视为成功
                        if (mMANAGE_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                        {
                            Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                            if (mWH_CELL_END != null && !string.IsNullOrEmpty(mPLAN_MAIN.PLAN_INOUT_STATION) &&
                                mWH_CELL_END.CELL_CODE == mPLAN_MAIN.PLAN_INOUT_STATION)
                            {
                                kitBoxCut += (string.Format("{0}-{1} ", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE));
                                kitBoxCutCount++;
                                continue;
                            }
                        }
                        /* mxh 25/8/21 如果所要齐套箱在移库任务中，则取消移库任务，下达出库任务*/
                        //需要增加对内外侧货位任务是否在同一个出库计划中的判断
                        else if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageMove.ToString())
                        {
                            
                            //先找内侧Manage任务
                            WH_CELL outerCell = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);   //当前正在倒库的外侧货位
                            WH_CELL innerCell = this._P_WH_CELL.GetModel(outerCell.CELL_GROUP);         //对位的内侧货位
                            STORAGE_MAIN storage = this._P_STORAGE_MAIN.GetModelCellID(innerCell.CELL_ID); //内侧库存主表

                            MANAGE_MAIN innerOutManage = this._P_MANAGE_MAIN.GetModelStockBarcode(storage.STOCK_BARCODE); //内侧货位的出库任务
                            if (innerOutManage == null)
                            {
                                bResult = false;
                                sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到内侧货位出库任务 箱条码_{0}", storage.STOCK_BARCODE);
                                return bResult;
                            }

                            bool isInSamePlan = false; //判断内外侧是否在同一个出库计划内

                            if (isInSamePlan = innerOutManage.PLAN_ID == mPLAN_MAIN.PLAN_ID)
                            {
                                /*1.取消外侧倒库任务(执行中的任务不可取消，兜底)*/
                                if (mMANAGE_MAIN.MANAGE_STATUS.Equals(Enum.MANAGE_STATUS.Executing.ToString()))
                                {
                                    bResult = false;
                                    sResult = $"PlanBase.PlanKitOutListExecute:外侧齐套箱倒库已执行 取消移库失败 箱条码_{mSTORAGE_MAIN.STOCK_BARCODE}";
                                    return bResult;
                                }
                                else
                                {
                                    this._P_IO_CONTROL.DeleteManageID(mMANAGE_MAIN.MANAGE_ID);
                                    bResult = this.Invoke("ManageBase", "ManageCancel_NoTrans", new object[] { mMANAGE_MAIN.MANAGE_ID }, out sResult);
                                    if (bResult == false)
                                    {
                                        sResult = string.Format("PlanBase.PlanKitOutListExecute:外侧齐套箱取消移库失败 箱条码_{0}", mSTORAGE_MAIN.STOCK_BARCODE);
                                        return bResult;
                                    }
                                }


                                //2.取消内侧出库任务(执行中的任务不可取消，兜底)
                                if (innerOutManage.MANAGE_STATUS.Equals(Enum.MANAGE_STATUS.Executing.ToString()))
                                {
                                    bResult = false;
                                    sResult = $"PlanBase.PlanKitOutListExecute:内侧齐套箱出库已执行 取消失败 箱条码_{innerOutManage.STOCK_BARCODE}";
                                    return bResult;
                                }
                                else 
                                {
                                    int line = this._P_IO_CONTROL.DeleteManageID(innerOutManage.MANAGE_ID);    //删除内侧货位出库任务
                                    bResult = this.Invoke("ManageBase", "ManageCancel_NoTrans", new object[] { innerOutManage.MANAGE_ID }, out sResult);
                                    if (bResult == false)
                                    {
                                        sResult = string.Format("PlanBase.PlanKitOutListExecute:内侧货位出库任务取消失败 箱条码_{0}", innerOutManage.STOCK_BARCODE);
                                        return bResult;
                                    }
                                }
                                    

                                //3.获取内侧货位的库存列表，用于重新创建管理任务
                                IList<Model.STORAGE_LIST> innerStorageList = this._P_STORAGE_LIST.GetListStorageID(storage.STORAGE_ID);
                                if (innerStorageList == null || innerStorageList.Count == 0)
                                {
                                    bResult = false;
                                    sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到内侧货位库存列表 库存ID_{0}", storage.STORAGE_ID);
                                    return bResult;
                                }

                                //4.重新创建完整的内侧出库管理任务（替换原有的ControlCreate调用）
                                Model.MANAGE_MAIN newInnerOutManage = new Model.MANAGE_MAIN();
                                newInnerOutManage.CELL_MODEL = storage.CELL_MODEL;
                                newInnerOutManage.END_CELL_ID = innerOutManage.END_CELL_ID;
                                newInnerOutManage.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                                newInnerOutManage.MANAGE_LEVEL = innerOutManage.MANAGE_LEVEL;
                                newInnerOutManage.MANAGE_OPERATOR = innerOutManage.MANAGE_OPERATOR;
                                newInnerOutManage.MANAGE_SOURCE = innerOutManage.MANAGE_SOURCE;
                                newInnerOutManage.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                                newInnerOutManage.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageOut.ToString();
                                newInnerOutManage.PLAN_ID = innerOutManage.PLAN_ID;
                                newInnerOutManage.PLAN_TYPE_CODE = innerOutManage.PLAN_TYPE_CODE;
                                newInnerOutManage.START_CELL_ID = storage.CELL_ID;
                                newInnerOutManage.STOCK_BARCODE = storage.STOCK_BARCODE;
                                newInnerOutManage.MANAGE_RELATE_CODE = innerOutManage.MANAGE_RELATE_CODE;
                                newInnerOutManage.MANAGE_REMARK = string.Format("齐套箱出库重新生成的内侧货位出库任务_原任务ID[{0}]", innerOutManage.MANAGE_ID);

                                //5.构建管理任务列表
                                List<Model.MANAGE_LIST> newInnerManageList = new List<Model.MANAGE_LIST>();
                                foreach (Model.STORAGE_LIST itemStorageList in innerStorageList)
                                {
                                    Model.MANAGE_LIST newManageList = new Model.MANAGE_LIST();
                                    newManageList = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemStorageList, newManageList, null);
                                    newManageList.MANAGE_LIST_QUANTITY = itemStorageList.STORAGE_LIST_QUANTITY;
                                    newInnerManageList.Add(newManageList);
                                }

                                //6.双深货位直接延迟策略：外侧货位有移库任务时，内侧任务延迟下达
                                string delayReason = string.Format("双深货位延迟出库_外侧货位[{0}]_移库任务ID[{1}]", outerCell.CELL_CODE, mMANAGE_MAIN.MANAGE_ID);
                                bResult = this.CreateDelayedInnerTask(newInnerOutManage, newInnerManageList, innerCell, outerCell, delayReason, out sResult);

                                if (!bResult)
                                {
                                    sResult = string.Format("PlanBase.PlanKitOutListExecute:齐套箱内侧货位延迟任务创建失败 箱条码_{0} 错误信息_{1}", storage.STOCK_BARCODE, sResult);
                                    return bResult;
                                }

                                //记录直接延迟的日志
                                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Information,
                                    string.Format("PlanBase.PlanKitOutListExecute:双深货位直接延迟策略_内侧任务延迟创建_箱条码[{0}]_内侧货位[{1}]_延迟原因[{2}]",
                                    newInnerOutManage.STOCK_BARCODE, innerCell.CELL_CODE, delayReason));
                            }
                        }
                        else //内外货位不在同一个计划中，则将外侧货位所属计划失败，让用户后续重新下达计划
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanKitOutListExecute:齐套箱在任务中无法出库 箱条码_{0}", mSTORAGE_MAIN.STOCK_BARCODE);
                            return bResult;
                        }
                    }

                    #region//start 2020-09-28 20:57:25 增加判断是否是双伸货位中的内层货位，如果是进一步查看外层库存和是否存在任务 

                    var storageCell = this._P_WH_CELL.GetModel(mSTORAGE_MAIN.CELL_ID);
                    if (storageCell == null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到库存所在的货位信息_箱条码[{0}]", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }
                    //如果齐套箱在双伸内侧
                    if (storageCell.SHELF_TYPE == Enum.CELL_FORK_TYPE.Double.ToString())
                    {
                        var storageCellNeighbour = this._P_WH_CELL.GetModel(storageCell.CELL_GROUP);
                        if (storageCellNeighbour == null)
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到库存所在的货位[{1}]的临近货位信息_箱条码[{0}]", mSTORAGE_MAIN.STOCK_BARCODE, storageCell.CELL_CODE);
                            return bResult;
                        }
                        var neighbourStorage = this._P_STORAGE_MAIN.GetModelCellID(storageCellNeighbour.CELL_ID);

                        //如果存在库存，并且没有任务，则下达倒库任务
                        if (neighbourStorage != null && storageCellNeighbour.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                        {
                            //获取双伸外侧库存的List
                            var neighbourStorageList = this._P_STORAGE_LIST.GetListStorageID(neighbourStorage.STORAGE_ID);
                            if (neighbourStorageList == null || neighbourStorageList.Count < 1)
                            {
                                bResult = false;
                                sResult = string.Format("PlanBase.PlanKitOutListExecute:未找到双伸外侧库存[{0}]的库存列表", neighbourStorage.STOCK_BARCODE);
                                return bResult;
                            }

                            //构建倒库任务
                            var manageMainMove = new Model.MANAGE_MAIN()
                            {
                                CROSS_FLAG = "0",
                                FULL_FLAG = "0",
                                GOODS_TEMPLATE_ID = 0,
                                MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                                MANAGE_CONFIRM_TIME = "",
                                MANAGE_END_TIME = "",
                                MANAGE_ID = 0,
                                MANAGE_LEVEL = "9",
                                MANAGE_OPERATOR = "自动倒库",
                                MANAGE_RELATE_CODE = "",
                                MANAGE_REMARK = $"{mSTORAGE_MAIN.STOCK_BARCODE}出库产生的倒库任务",
                                MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString(),
                                MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString(),
                                MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageMove.ToString(),
                                PLAN_ID = 0,
                                PLAN_TYPE_CODE = "",
                                START_CELL_ID = neighbourStorage.CELL_ID,
                                STOCK_BARCODE = neighbourStorage.STOCK_BARCODE,
                                CELL_MODEL = neighbourStorage.CELL_MODEL,
                                END_CELL_ID = 0
                            };
                            var manageListsMove = new List<Model.MANAGE_LIST>();

                            foreach (var item in neighbourStorageList)
                            {
                                manageListsMove.Add(new Model.MANAGE_LIST()
                                {
                                    BOX_BARCODE = item.BOX_BARCODE,
                                    DETAIL_FLAG = "0",
                                    MANAGE_ID = 0,
                                    MANAGE_LIST_ID = 0,
                                    MANAGE_LIST_QUANTITY = item.STORAGE_LIST_QUANTITY,
                                    MANAGE_LIST_REMARK = "",
                                    PLAN_LIST_ID = 0,
                                    STORAGE_LIST_ID = item.STORAGE_LIST_ID,
                                    STORAGE_LOCK_ID = 0,
                                    GOODS_ID = item.GOODS_ID,
                                    GOODS_PROPERTY1 = item.GOODS_PROPERTY1,
                                    GOODS_PROPERTY2 = item.GOODS_PROPERTY2,
                                    GOODS_PROPERTY3 = item.GOODS_PROPERTY3,
                                    GOODS_PROPERTY4 = item.GOODS_PROPERTY4,
                                    GOODS_PROPERTY5 = item.GOODS_PROPERTY5,
                                    GOODS_PROPERTY6 = item.GOODS_PROPERTY6,
                                    GOODS_PROPERTY7 = item.GOODS_PROPERTY7,
                                    GOODS_PROPERTY8 = item.GOODS_PROPERTY8
                                });
                            }

                            //使用双伸货位分配方法分配一个空闲货位
                            int newEndCellId = 0;
                            //storageCellNeighbour
                            WH_CELL_GROUP mWH_CELL_GROUP = _P_WH_CELL_GROUP.GetModelByGroupCode(storageCellNeighbour.CELL_CODE.Substring(3));
                            bResult = this._S_CellService.RelocateInGroup(storageCellNeighbour, mWH_CELL_GROUP, out newEndCellId, out sResult);
                            //bResult = this._S_CellService.CellInAllocate_ThirdFloor(
                            //    storageCellNeighbour.WAREHOUSE_ID,
                            //    storageCellNeighbour.AREA_ID,
                            //    neighbourStorageList[0].GOODS_PROPERTY6,
                            //    neighbourStorageList[0].GOODS_PROPERTY7,
                            //    neighbourStorageList[0].GOODS_PROPERTY8,
                            //    out newEndCellId,
                            //    out sResult);

                            if (!bResult || newEndCellId <= 0)
                            {
                                sResult = string.Format("PlanBase.PlanKitOutListExecute:双伸货位外侧库存[{0}]倒库时未能分配新货位_信息[{1}]", neighbourStorage.STOCK_BARCODE, sResult);
                                return bResult;
                            }

                            manageMainMove.END_CELL_ID = newEndCellId;

                            bResult = new ManageMove().ManageCreate(manageMainMove, manageListsMove, false, true, false, true, out sResult);
                            if (!bResult)
                            {
                                sResult = string.Format("PlanBase.PlanKitOutListExecute:双伸货位外侧库存[{0}]倒库时下达任务失败_信息[{1}]", neighbourStorage.STOCK_BARCODE, sResult);
                                return bResult;
                            }
                        }
                        //如果不存在库存，并且有任务，则提示失败，因为马上会有入库的任务
                        else if (neighbourStorage == null && storageCellNeighbour.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                        {
                            bResult = false;
                            sResult = string.Format("PlanBase.PlanKitOutListExecute:无法出库_齐套箱[{0}]在双伸货位内侧_并且外侧货位[{1}]有入库任务", mSTORAGE_MAIN.STOCK_BARCODE, storageCellNeighbour.CELL_CODE);
                            return bResult;
                        }
                        //如果存在库存，并且有任务，则说明有出库任务，不理会直接下内层任务，调度控制先后
                        //如果不存在库存，并且没有任务，直接下达内层任务，因为没有任何障碍
                        else { }
                    }
                    #endregion end 2020-09-28 20:57:25 增加判断是否是双伸货位中的内层货位，如果是进一步查看外层库存和是否存在任务

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = endCellId;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = mPLAN_MAIN.PLAN_LEVEL;
                    mMANAGE_MAIN.MANAGE_OPERATOR = mPLAN_MAIN.PLAN_CREATER;
                    mMANAGE_MAIN.MANAGE_SOURCE = mPLAN_MAIN.PLAN_FLAG;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageOut.ToString();
                    mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                    mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = mPLAN_MAIN.PLAN_CODE;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                        mMANAGE_LIST.STORAGE_LIST_ID = itemSTORAGE_LIST.STORAGE_LIST_ID;
                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }
                    #region 5楼、3楼出库业务分流逻辑
                    Model.WH_CELL mSTART_CELL = _P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                    if (null == mSTART_CELL)
                    {
                        bResult = false;
                        sResult = string.Format(@"PlanBase.PlanKitOutListExecute:查找5楼起始位置失败");
                        return bResult;
                    }
                    //获取3楼线边仓ID
                    string Floor3WarehouseID = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("Floor3WarehouseID", out Floor3WarehouseID) ||
                        string.IsNullOrEmpty(Floor3WarehouseID))
                    {
                        bResult = false;
                        sResult = "PlanBase.PlanKitOutListExecute:未找到系统参数[Floor3WarehouseID]";
                        return bResult;
                    }
                    //获取3楼提升机出口站台
                    string MoveToFloor3EndStation = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("MoveToFloor3EndStation", out MoveToFloor3EndStation) ||
                        string.IsNullOrEmpty(MoveToFloor3EndStation))
                    {
                        bResult = false;
                        sResult = "PlanBase.PlanKitOutListExecute:未找到系统参数[MoveToFloor3EndStation]";
                        return bResult;
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    /**
                     * KitBoxOut齐套箱出库接口（计划），允许库存地点在5楼和3楼的齐套箱直接出库到3楼线边仓的出库站台
                     * 对于库存地点在5楼的齐套箱，需将控制(调度)任务分段下达：
                     *  1.货位-至-81015三楼提升机出口站台
                     *  2.经81050扫码器申请后，修改对应管理任务的起点位置，并调用ManageDownload方法下达调度任务（代码位置：ApplyAllocateCell3）
                     * **/
                    //库存地点在5楼&&出库位置在3楼出库站台
                    if (!mSTART_CELL.WAREHOUSE_ID.ToString().Equals(Floor3WarehouseID) &&
                        (mPLAN_MAIN.PLAN_INOUT_STATION.Equals("81098") || mPLAN_MAIN.PLAN_INOUT_STATION.Equals("81056") 
                        || mPLAN_MAIN.PLAN_INOUT_STATION.Equals("81070") || mPLAN_MAIN.PLAN_INOUT_STATION.Equals("81084")))
                    {
                        
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, false, false }, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }
                        bResult = this._S_ManageService.ControlCreate(2, mMANAGE_MAIN.STOCK_BARCODE, "1", mSTART_CELL.CELL_CODE, "3", MoveToFloor3EndStation, "5", out sResult, true, false);
                    }
                    //原有逻辑（兼容3楼出库）
                    else
                    {
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, true, false }, out sResult);
                    }
                    
                    if (!bResult)
                    {
                        return bResult;
                    }
                    #endregion 5楼、3楼出库业务分流逻辑
                }

                if (kitBoxCutCount > 0)
                {
                    this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("PlanBase.PlanKitOutListExecute:当前齐套箱出库计划所需物料包含在以下出库任务和料箱中[{0}]", kitBoxCut));
                }

                mPLAN_LIST.PLAN_LIST_QUANTITY = mPLAN_LIST.PLAN_LIST_QUANTITY - kitBoxCutCount;
                mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_QUANTITY = dtStorageEnable.Rows.Count - kitBoxCutCount;

                if (mPLAN_LIST.PLAN_LIST_QUANTITY < 1)
                {
                    //如果因为齐套箱所需物料包含在其他齐套箱出库任务中，并且导致计划单数量已清零
                    //（即这个计划单里所需的所有物料都在其他相同出库位置的齐套箱出库任务中），则删除这个计划单
                    this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("PlanBase.PlanKitOutListExecute:当前齐套箱出库计划所需物料均以包含在其他相同出库位置的齐套出库任务中_本计划单删除_计划单ID[{0}]_拣选任务单号[{1}]_项目号[{2}]_WBS号[{3}]", mPLAN_LIST.PLAN_LIST_ID, mPLAN_LIST.GOODS_PROPERTY6, mPLAN_LIST.GOODS_PROPERTY7, mPLAN_LIST.GOODS_PROPERTY8));
                    this._P_PLAN_LIST.Delete(mPLAN_LIST.PLAN_LIST_ID);
                }
                else
                {
                    this._P_PLAN_LIST.Update(mPLAN_LIST);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.PlanKitOutListExecute:程序执行中发生异常 信息_{0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// 合箱出库计划单执行
        /// </summary>
        public bool PlanArrangeOutListExecute(Model.PLAN_LIST mPLAN_LIST, Model.PLAN_MAIN mPLAN_MAIN, int endCellId, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID);
                if (mGOODS_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanArrangeOutListExecute:未找到计划物料_物料ID[{0}]_计划列表ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                    return bResult;
                }
                if (mGOODS_MAIN.GOODS_CODE != "arrangeTask")
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanArrangeOutListExecute:合箱出库计划单中的虚拟物料有误_计划单物料编码[{0}]", mGOODS_MAIN.GOODS_CODE);
                    return bResult;
                }

                // 2019-05-16 by ywz 增加库区作为排序条件
                Model.WH_CELL endCell = this._P_WH_CELL.GetModel(mPLAN_MAIN.PLAN_INOUT_STATION);
                if (endCell == null)
                {
                    bResult = false;
                    sResult = string.Format("PlanBase.PlanArrangeOutListExecute:合箱出库计划出库站台有误_站台编码[{0}]", mPLAN_MAIN.PLAN_INOUT_STATION);
                    return bResult;
                }
                string sql = string.Format(string.Format(@"select STOCK_BARCODE， LANE_WAY, BOX_TYPE, EMPTY_CAPACITY,AREA_PRIORITY,
                                                                  (select count(0) from V_MANAGE where START_LANE_WAY=LANE_WAY) as MANAGE_OUT_COUNT       
                                                           from (select STOCK_BARCODE,
                                                                        min(LANE_WAY) as LANE_WAY,
                                                                        min(CELL_MODEL) as BOX_TYPE,
                                                                        (case when min(AREA_ID)={3} then 1 else 0 end) as AREA_PRIORITY,
                                                                        (case min(CELL_MODEL) when '1' then 1 when '2' then 2 when '3' then 3 when '4' then 4 when '6' then 6 else 0 end -count(STOCK_BARCODE)) as EMPTY_CAPACITY 
                                                                 from V_STORAGE_LIST t
                                                                 where CELL_TYPE='Cell' and RUN_STATUS='Enable' and CELL_STATUS in ('Full','Pallet')
                                                                       and STORAGE_ID not in (select STORAGE_ID from STORAGE_LIST where STORAGE_LIST_ID in (select STORAGE_LIST_ID from STORAGE_LOCK))
                                                                       and STORAGE_ID not in (select STORAGE_ID from STORAGE_LIST where GOODS_PROPERTY2 is not null and GOODS_PROPERTY2 ='1')
                                                                       and (IS_EXCEPTION is null or IS_EXCEPTION!='1')
                                                                       and (DEVICE_CODE in (select START_DEVICE from IO_CONTROL_ROUTE where END_DEVICE='{2}' and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE=1))
                                                                 group by STOCK_BARCODE) 
                                                           where {0} and {1}
                                                           order by EMPTY_CAPACITY/BOX_TYPE desc",
                                                           string.IsNullOrEmpty(mPLAN_LIST.GOODS_PROPERTY1) ? "1=1" : string.Format("BOX_TYPE = '{0}'", mPLAN_LIST.GOODS_PROPERTY1),
                                                           string.IsNullOrEmpty(mPLAN_LIST.GOODS_PROPERTY2) ? "EMPTY_CAPACITY>=1" : string.Format("EMPTY_CAPACITY>='{0}'", mPLAN_LIST.GOODS_PROPERTY2),
                                                           mPLAN_MAIN.PLAN_INOUT_STATION,
                                                           endCell.CELL_LOGICAL_NAME));

                while (mPLAN_LIST.PLAN_LIST_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                {
                    DataTable dtStorageEnable = this.GetList(sql);
                    if (dtStorageEnable == null || dtStorageEnable.Rows.Count < mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanArrangeOutListExecute:满足条件的待整理箱数量不足_计划单号[{0}]_库存箱数[{1}]_计划数量[{2}]", mPLAN_MAIN.PLAN_CODE, dtStorageEnable == null ? 0 : dtStorageEnable.Rows.Count, mPLAN_LIST.PLAN_LIST_QUANTITY);
                        return bResult;
                    }

                    DataView dvStorageEnbale = dtStorageEnable.DefaultView;

                    // 2019-05-16 by ywz 增加库区作为排序条件
                    //以出库任务数降序、空余格子升序排列
                    dvStorageEnbale.Sort = "AREA_PRIORITY desc ,MANAGE_OUT_COUNT asc,EMPTY_CAPACITY desc";

                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(dvStorageEnbale[0]["STOCK_BARCODE"].ToString());
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanArrangeOutListExecute:未找到立库库存_容器条码[{0}]", dvStorageEnbale[0]["STOCK_BARCODE"]);
                        return bResult;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("PlanBase.PlanArrangeOutListExecute:未找到库存列表_容器条码[{0}]", dvStorageEnbale[0]["STOCK_BARCODE"]);
                        return bResult;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = endCellId;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = mPLAN_MAIN.PLAN_LEVEL;
                    mMANAGE_MAIN.MANAGE_OPERATOR = mPLAN_MAIN.PLAN_CREATER;
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WMS.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageArrangeDown.ToString();
                    mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                    mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, false, true }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY++;
                    this._P_PLAN_LIST.Update(mPLAN_LIST);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.PlanArrangeOutListExecute:程序执行中发生异常_信息[{0}]", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        #region 双深货位任务创建延迟策略相关方法

        /// <summary>
        /// 创建延迟的内侧任务记录
        /// </summary>
        /// <param name="newInnerOutManage">内侧出库管理任务对象</param>
        /// <param name="newInnerManageList">内侧出库管理任务列表</param>
        /// <param name="innerCell">内侧货位信息</param>
        /// <param name="outerCell">外侧货位信息</param>
        /// <param name="outerTaskInfo">外侧任务信息</param>
        /// <param name="sResult">返回结果</param>
        /// <returns>是否成功</returns>
        private bool CreateDelayedInnerTask(Model.MANAGE_MAIN newInnerOutManage,
            List<Model.MANAGE_LIST> newInnerManageList,
            WH_CELL innerCell,
            WH_CELL outerCell,
            string outerTaskInfo,
            out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                // 1. 设置管理任务为延迟创建状态
                newInnerOutManage.MANAGE_STATUS = "PendingControlCreate";
                // 优化备注格式：体现基于CONTROL_ID顺序执行的技术保证，移除冗余的"等待"概念
                newInnerOutManage.MANAGE_REMARK = string.Format(
                    "双深货位延迟出库_外侧货位[{0}]_延迟创建时间[{1}]",
                    outerCell.CELL_CODE, Common.StringUtil.GetDateTime());

                // 2. 创建管理任务记录（不创建控制任务）
                bResult = new ManageOut().ManageCreate(newInnerOutManage, newInnerManageList, false, false, false, out sResult);

                if (bResult)
                {
                    sResult = string.Format("双深货位直接延迟任务创建成功_基于CONTROL_ID顺序保证_箱条码[{0}]_管理任务ID[{1}]_外侧货位[{2}]",
                        newInnerOutManage.STOCK_BARCODE, newInnerOutManage.MANAGE_ID, outerCell.CELL_CODE);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("PlanBase.CreateDelayedInnerTask:创建延迟任务记录异常 {0}", ex.Message);
            }

            return bResult;
        }

        #endregion

    }
}
